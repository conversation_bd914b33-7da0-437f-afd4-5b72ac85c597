# AlgoTrader Frontend Development Container - Working Version
# Multi-stage build optimized for Windows development environment
# Last Updated: 2024-12-19

# Stage 1: Base Dependencies
FROM node:22.15.0-alpine AS deps

# Install system dependencies
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    py3-pip \
    make \
    g++ \
    git \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm install --legacy-peer-deps --no-audit --no-fund && \
    npm cache clean --force

# Stage 2: Development Environment
FROM node:22.15.0-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    py3-pip \
    make \
    g++ \
    git \
    curl \
    wget \
    ca-certificates \
    bash \
    dumb-init \
    su-exec \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/package*.json ./

# Note: Configuration files and source code will be mounted as volumes from host
# This container only provides the development environment and tools

# Create comprehensive development scripts with proper permissions
RUN mkdir -p /usr/local/bin && \
    echo '#!/bin/bash' > /usr/local/bin/healthcheck.sh && \
    echo 'set -e' >> /usr/local/bin/healthcheck.sh && \
    echo 'if curl -f -s --max-time 10 http://localhost:${PORT:-3000}/api/health 2>/dev/null || \\' >> /usr/local/bin/healthcheck.sh && \
    echo '   curl -f -s --max-time 10 http://localhost:${PORT:-3000} 2>/dev/null; then' >> /usr/local/bin/healthcheck.sh && \
    echo '  echo "Health check passed"' >> /usr/local/bin/healthcheck.sh && \
    echo '  exit 0' >> /usr/local/bin/healthcheck.sh && \
    echo 'else' >> /usr/local/bin/healthcheck.sh && \
    echo '  echo "Health check failed - service not responding"' >> /usr/local/bin/healthcheck.sh && \
    echo '  exit 1' >> /usr/local/bin/healthcheck.sh && \
    echo 'fi' >> /usr/local/bin/healthcheck.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# Create production-level startup script
RUN echo '#!/bin/bash' > /usr/local/bin/start-dev.sh && \
    echo 'set -e' >> /usr/local/bin/start-dev.sh && \
    echo '' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "=== AlgoTrader Frontend Development Environment ==="' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "Node.js version: $(node --version)"' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "NPM version: $(npm --version)"' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "Environment: ${NODE_ENV:-development}"' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "Port: ${PORT:-3000}"' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "User: $(whoami)"' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "Working Directory: $(pwd)"' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "================================================"' >> /usr/local/bin/start-dev.sh && \
    echo '' >> /usr/local/bin/start-dev.sh && \
    echo '# Ensure proper permissions' >> /usr/local/bin/start-dev.sh && \
    echo 'if [ "$(whoami)" = "root" ]; then' >> /usr/local/bin/start-dev.sh && \
    echo '  echo "Warning: Running as root. Switching to nextjs user..."' >> /usr/local/bin/start-dev.sh && \
    echo '  exec su-exec nextjs "$0" "$@"' >> /usr/local/bin/start-dev.sh && \
    echo 'fi' >> /usr/local/bin/start-dev.sh && \
    echo '' >> /usr/local/bin/start-dev.sh && \
    echo '# Install dependencies if node_modules is missing or incomplete' >> /usr/local/bin/start-dev.sh && \
    echo 'if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then' >> /usr/local/bin/start-dev.sh && \
    echo '  echo "Installing/updating dependencies..."' >> /usr/local/bin/start-dev.sh && \
    echo '  npm install --legacy-peer-deps --no-audit --no-fund' >> /usr/local/bin/start-dev.sh && \
    echo '  echo "Dependencies installed successfully"' >> /usr/local/bin/start-dev.sh && \
    echo 'fi' >> /usr/local/bin/start-dev.sh && \
    echo '' >> /usr/local/bin/start-dev.sh && \
    echo '# Clear Next.js cache for fresh start' >> /usr/local/bin/start-dev.sh && \
    echo 'if [ -d ".next" ]; then' >> /usr/local/bin/start-dev.sh && \
    echo '  echo "Clearing Next.js cache..."' >> /usr/local/bin/start-dev.sh && \
    echo '  rm -rf .next' >> /usr/local/bin/start-dev.sh && \
    echo 'fi' >> /usr/local/bin/start-dev.sh && \
    echo '' >> /usr/local/bin/start-dev.sh && \
    echo '# Start the development server with enhanced configuration' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "Starting Next.js development server with hot-reloading..."' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "Access the application at: http://localhost:${PORT:-3000}"' >> /usr/local/bin/start-dev.sh && \
    echo 'echo "================================================"' >> /usr/local/bin/start-dev.sh && \
    echo '' >> /usr/local/bin/start-dev.sh && \
    echo '# Use exec to replace the shell process' >> /usr/local/bin/start-dev.sh && \
    echo 'exec npm run dev' >> /usr/local/bin/start-dev.sh && \
    chmod +x /usr/local/bin/start-dev.sh

# Create development utilities script
RUN echo '#!/bin/bash' > /usr/local/bin/dev-tools.sh && \
    echo 'set -e' >> /usr/local/bin/dev-tools.sh && \
    echo '' >> /usr/local/bin/dev-tools.sh && \
    echo 'case "$1" in' >> /usr/local/bin/dev-tools.sh && \
    echo '  "debug")' >> /usr/local/bin/dev-tools.sh && \
    echo '    echo "Starting development server in debug mode..."' >> /usr/local/bin/dev-tools.sh && \
    echo '    NODE_OPTIONS="--inspect=0.0.0.0:9229" npm run dev' >> /usr/local/bin/dev-tools.sh && \
    echo '    ;;' >> /usr/local/bin/dev-tools.sh && \
    echo '  "test")' >> /usr/local/bin/dev-tools.sh && \
    echo '    echo "Running tests..."' >> /usr/local/bin/dev-tools.sh && \
    echo '    npm run test' >> /usr/local/bin/dev-tools.sh && \
    echo '    ;;' >> /usr/local/bin/dev-tools.sh && \
    echo '  "lint")' >> /usr/local/bin/dev-tools.sh && \
    echo '    echo "Running linter..."' >> /usr/local/bin/dev-tools.sh && \
    echo '    npm run lint' >> /usr/local/bin/dev-tools.sh && \
    echo '    ;;' >> /usr/local/bin/dev-tools.sh && \
    echo '  "build")' >> /usr/local/bin/dev-tools.sh && \
    echo '    echo "Building application..."' >> /usr/local/bin/dev-tools.sh && \
    echo '    npm run build' >> /usr/local/bin/dev-tools.sh && \
    echo '    ;;' >> /usr/local/bin/dev-tools.sh && \
    echo '  *)' >> /usr/local/bin/dev-tools.sh && \
    echo '    echo "Available commands: debug, test, lint, build"' >> /usr/local/bin/dev-tools.sh && \
    echo '    ;;' >> /usr/local/bin/dev-tools.sh && \
    echo 'esac' >> /usr/local/bin/dev-tools.sh && \
    chmod +x /usr/local/bin/dev-tools.sh

# Ensure all scripts have proper ownership and permissions
RUN chown root:root /usr/local/bin/healthcheck.sh /usr/local/bin/start-dev.sh /usr/local/bin/dev-tools.sh && \
    chmod 755 /usr/local/bin/healthcheck.sh /usr/local/bin/start-dev.sh /usr/local/bin/dev-tools.sh

# Set ownership for application directory
RUN chown -R nextjs:nodejs /app

# Environment variables
ENV NODE_ENV=development
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NEXT_PUBLIC_ENABLE_SW_IN_DEV=false
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true
ENV FAST_REFRESH=true
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Expose ports
EXPOSE 3000 9229

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=5 \
    CMD ["/usr/local/bin/healthcheck.sh"]

# Switch to non-root user
USER nextjs

# Default command
CMD ["/usr/local/bin/start-dev.sh"]
