# AlgoTrader Frontend - Hybrid Development Setup

## Overview

This document explains the **hybrid development setup** for the AlgoTrader frontend, which is the correct and preferred approach for development containers.

## Architecture

### 🐳 **Docker Container (Environment Only)**
- **Node.js 22.15.0** runtime environment
- **System dependencies** (build tools, libraries)
- **npm dependencies** (installed and cached)
- **Development tools** and scripts
- **Hot-reloading configuration**
- **Debugging capabilities**

### 💻 **Local Machine (Codebase)**
- **Source code** (`src/`, `pages/`, `app/`, etc.)
- **Configuration files** (`next.config.js`, `package.json`, etc.)
- **Static assets** (`public/`)
- **Documentation** and project files

## Benefits of Hybrid Setup

### ✅ **Development Advantages**
- **Real-time editing**: Changes on local machine instantly reflect in container
- **IDE integration**: Full IDE features (IntelliSense, debugging, extensions)
- **File system performance**: No Docker volume performance overhead
- **Git integration**: Direct git operations on local files
- **Backup and sync**: Local files are easily backed up and synced

### ✅ **Container Advantages**
- **Consistent environment**: Same Node.js version and dependencies for all developers
- **Isolated dependencies**: No conflicts with local system
- **Hot-reloading**: Optimized file watching and fast refresh
- **Production parity**: Same environment as production containers
- **Easy cleanup**: Remove container without affecting source code

## Volume Mounting Strategy

### **Source Code Volumes**
```yaml
volumes:
  - ./frontend:/app:cached  # Mount entire frontend directory
  - frontend_dev_node_modules:/app/node_modules  # Persistent dependencies
  - frontend_dev_next:/app/.next  # Persistent build cache
```

### **Performance Optimizations**
- **Cached mounting**: `:cached` flag for better performance on macOS/Windows
- **Persistent volumes**: `node_modules` and `.next` in named volumes
- **Selective mounting**: Configuration files mounted read-only when needed

## Development Workflow

### **1. Initial Setup**
```bash
# Build the environment container
docker-compose build frontend-dev

# Start the development environment
docker-compose up -d frontend-dev
```

### **2. Development Process**
```bash
# Edit files locally with your preferred IDE
# Changes are automatically detected and hot-reloaded

# Monitor development server
docker-compose logs -f frontend-dev

# Access application
# http://localhost:3000
```

### **3. Container Management**
```bash
# Use the management script
cd frontend
./dev-container-manager.sh status
./dev-container-manager.sh logs -f
./dev-container-manager.sh shell
```

## File Structure

### **Container (`/app`)**
```
/app/
├── node_modules/          # From container build
├── .next/                 # Build cache (persistent volume)
├── package.json           # Mounted from host
├── next.config.js         # Mounted from host
├── src/                   # Mounted from host
├── public/                # Mounted from host
└── ...                    # All other files mounted from host
```

### **Local Machine**
```
frontend/
├── src/                   # Your source code
├── public/                # Static assets
├── package.json           # Dependencies definition
├── next.config.js         # Next.js configuration
├── Dockerfile.dev.working # Container environment definition
└── ...                    # All project files
```

## Hot-Reloading Configuration

### **Environment Variables**
```bash
WATCHPACK_POLLING=true      # File watching optimization
CHOKIDAR_USEPOLLING=true    # Cross-platform file watching
FAST_REFRESH=true           # React Fast Refresh
NODE_OPTIONS=--max-old-space-size=4096  # Memory optimization
```

### **Next.js Configuration**
- **Source maps**: `eval-source-map` for debugging
- **File watching**: Optimized polling for Docker environments
- **Fast refresh**: React component hot-reloading
- **Development optimizations**: Webpack dev-specific settings

## Debugging Setup

### **Node.js Debugging**
- **Port 9229**: Exposed for IDE debugging
- **Source maps**: Available for breakpoint debugging
- **Inspector**: `--inspect=0.0.0.0:9229` flag support

### **IDE Integration**
```json
// VS Code launch.json
{
  "type": "node",
  "request": "attach",
  "name": "Docker: Attach to Node",
  "remoteRoot": "/app",
  "localRoot": "${workspaceFolder}/frontend",
  "port": 9229
}
```

## Container Scripts

### **Startup Script** (`/usr/local/bin/start-dev.sh`)
- Environment validation
- Dependency installation (if needed)
- Cache clearing
- Development server startup

### **Health Check** (`/usr/local/bin/healthcheck.sh`)
- Server responsiveness check
- API endpoint validation
- Container health monitoring

### **Development Tools** (`/usr/local/bin/dev-tools.sh`)
- Debug mode activation
- Test execution
- Linting and type checking
- Build validation

## Troubleshooting

### **Common Issues**

#### **Hot-reloading not working**
```bash
# Check file watching settings
docker exec algotrader-frontend-dev env | grep WATCH

# Restart container
docker-compose restart frontend-dev
```

#### **Dependencies out of sync**
```bash
# Rebuild container with fresh dependencies
docker-compose build --no-cache frontend-dev
```

#### **Performance issues**
```bash
# Check container resources
docker stats algotrader-frontend-dev

# Clean up volumes
docker-compose down
docker volume prune
```

## Best Practices

### **Development**
1. **Edit locally**: Use your preferred IDE on local files
2. **Monitor container**: Keep logs open to see hot-reload status
3. **Use management script**: Leverage provided tools for container operations
4. **Regular cleanup**: Periodically clean Docker volumes and images

### **Performance**
1. **Exclude large directories**: Use `.dockerignore` for unnecessary files
2. **Use cached volumes**: Leverage persistent volumes for dependencies
3. **Monitor resources**: Keep an eye on container memory and CPU usage
4. **Optimize file watching**: Use polling settings for cross-platform compatibility

### **Security**
1. **Non-root execution**: Container runs as `nextjs` user
2. **Volume permissions**: Proper ownership and permissions
3. **Network isolation**: Container network separation
4. **Resource limits**: Memory and CPU constraints

## Summary

This hybrid setup provides the best of both worlds:
- **Local development experience** with full IDE integration
- **Consistent containerized environment** for all team members
- **Production parity** without sacrificing development speed
- **Hot-reloading and debugging** capabilities
- **Easy maintenance** and cleanup

The container serves as a **development environment provider** while your local machine remains the **source of truth** for the codebase.
